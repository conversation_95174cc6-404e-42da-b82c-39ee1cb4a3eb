#!/bin/bash

# MCP Service Manager 管理脚本
# 用于启动、停止、重启和查看日志

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$SCRIPT_DIR"
VENV_DIR="$PROJECT_DIR/.venv"
PID_FILE="$PROJECT_DIR/mcp_service_manager.pid"
LOG_FILE="$PROJECT_DIR/logs/mcp_service_manager.log"
SERVICE_NAME="mcp-service-manager"

# 默认配置
DEFAULT_HOST="0.0.0.0"
DEFAULT_PORT="8000"
DEFAULT_LOG_LEVEL="info"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查虚拟环境
check_venv() {
    if [ ! -d "$VENV_DIR" ]; then
        print_error "虚拟环境不存在: $VENV_DIR"
        print_info "请先运行: uv venv"
        return 1
    fi
    return 0
}

# 激活虚拟环境
activate_venv() {
    if [ -f "$VENV_DIR/bin/activate" ]; then
        source "$VENV_DIR/bin/activate"
    elif [ -f "$VENV_DIR/Scripts/activate" ]; then
        source "$VENV_DIR/Scripts/activate"
    else
        print_error "无法找到虚拟环境激活脚本"
        return 1
    fi
    return 0
}

# 检查服务是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            # PID文件存在但进程不存在，清理PID文件
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 获取服务状态
get_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        print_success "$SERVICE_NAME 正在运行 (PID: $pid)"
        
        # 检查端口是否监听
        if command -v netstat >/dev/null 2>&1; then
            local port_info=$(netstat -tlnp 2>/dev/null | grep ":$DEFAULT_PORT " | head -1)
            if [ -n "$port_info" ]; then
                print_info "监听端口: $DEFAULT_PORT"
                print_info "Web界面: http://localhost:$DEFAULT_PORT"
                print_info "API文档: http://localhost:$DEFAULT_PORT/docs"
            fi
        fi
        return 0
    else
        print_warning "$SERVICE_NAME 未运行"
        return 1
    fi
}

# 启动服务
start_service() {
    local host="${1:-$DEFAULT_HOST}"
    local port="${2:-$DEFAULT_PORT}"
    local log_level="${3:-$DEFAULT_LOG_LEVEL}"
    
    print_info "启动 $SERVICE_NAME..."
    
    # 检查是否已经运行
    if is_running; then
        print_warning "$SERVICE_NAME 已经在运行"
        get_status
        return 0
    fi
    
    # 检查虚拟环境
    if ! check_venv; then
        return 1
    fi
    
    # 切换到项目目录
    cd "$PROJECT_DIR" || {
        print_error "无法切换到项目目录: $PROJECT_DIR"
        return 1
    }
    
    # 激活虚拟环境
    if ! activate_venv; then
        return 1
    fi
    
    # 确保日志目录存在
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 启动服务
    print_info "启动参数: --host $host --port $port --log-level $log_level"
    
    nohup python -m src.mcp_service_manager.main \
        --host "$host" \
        --port "$port" \
        --log-level "$log_level" \
        > "$LOG_FILE" 2>&1 &
    
    local pid=$!
    echo "$pid" > "$PID_FILE"
    
    # 等待服务启动
    sleep 3
    
    if is_running; then
        print_success "$SERVICE_NAME 启动成功 (PID: $pid)"
        print_info "Web界面: http://$host:$port"
        print_info "API文档: http://$host:$port/docs"
        print_info "日志文件: $LOG_FILE"
    else
        print_error "$SERVICE_NAME 启动失败"
        print_info "请查看日志: $LOG_FILE"
        rm -f "$PID_FILE"
        return 1
    fi
}

# 停止服务
stop_service() {
    print_info "停止 $SERVICE_NAME..."
    
    if ! is_running; then
        print_warning "$SERVICE_NAME 未运行"
        return 0
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # 尝试优雅关闭
    print_info "发送 TERM 信号到进程 $pid"
    kill -TERM "$pid" 2>/dev/null
    
    # 等待进程结束
    local count=0
    while [ $count -lt 10 ]; do
        if ! ps -p "$pid" > /dev/null 2>&1; then
            break
        fi
        sleep 1
        count=$((count + 1))
    done
    
    # 如果进程仍在运行，强制杀死
    if ps -p "$pid" > /dev/null 2>&1; then
        print_warning "优雅关闭失败，强制终止进程"
        kill -KILL "$pid" 2>/dev/null
        sleep 2
    fi
    
    # 清理PID文件
    rm -f "$PID_FILE"
    
    if ! ps -p "$pid" > /dev/null 2>&1; then
        print_success "$SERVICE_NAME 已停止"
    else
        print_error "无法停止 $SERVICE_NAME"
        return 1
    fi
}

# 重启服务
restart_service() {
    print_info "重启 $SERVICE_NAME..."
    stop_service
    sleep 2
    start_service "$@"
}

# 查看日志
show_logs() {
    local lines="${1:-50}"
    local follow="${2:-false}"
    
    if [ ! -f "$LOG_FILE" ]; then
        print_warning "日志文件不存在: $LOG_FILE"
        return 1
    fi
    
    print_info "显示日志: $LOG_FILE"
    
    if [ "$follow" = "true" ]; then
        print_info "实时跟踪日志 (按 Ctrl+C 退出)"
        tail -f "$LOG_FILE"
    else
        print_info "显示最后 $lines 行日志"
        tail -n "$lines" "$LOG_FILE"
    fi
}

# 清理日志
clean_logs() {
    print_info "清理日志文件..."
    
    if [ -f "$LOG_FILE" ]; then
        > "$LOG_FILE"
        print_success "日志文件已清理: $LOG_FILE"
    else
        print_warning "日志文件不存在: $LOG_FILE"
    fi
    
    # 清理其他可能的日志文件
    if [ -d "$PROJECT_DIR/logs" ]; then
        find "$PROJECT_DIR/logs" -name "service_*.log" -type f -exec truncate -s 0 {} \;
        print_info "已清理所有服务日志文件"
    fi
}

# 显示帮助信息
show_help() {
    echo "MCP Service Manager 管理脚本"
    echo ""
    echo "用法: $0 <命令> [选项]"
    echo ""
    echo "命令:"
    echo "  start [host] [port] [log_level]  启动服务 (默认: 0.0.0.0 8000 info)"
    echo "  stop                             停止服务"
    echo "  restart [host] [port] [log_level] 重启服务"
    echo "  status                           查看服务状态"
    echo "  logs [lines]                     查看日志 (默认显示最后50行)"
    echo "  logs-follow                      实时跟踪日志"
    echo "  clean-logs                       清理日志文件"
    echo "  help                             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                         # 使用默认配置启动"
    echo "  $0 start 127.0.0.1 8080 debug   # 自定义配置启动"
    echo "  $0 logs 100                      # 查看最后100行日志"
    echo "  $0 logs-follow                   # 实时跟踪日志"
    echo ""
    echo "配置:"
    echo "  项目目录: $PROJECT_DIR"
    echo "  虚拟环境: $VENV_DIR"
    echo "  PID文件: $PID_FILE"
    echo "  日志文件: $LOG_FILE"
}

# 主函数
main() {
    case "${1:-help}" in
        start)
            start_service "$2" "$3" "$4"
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service "$2" "$3" "$4"
            ;;
        status)
            get_status
            ;;
        logs)
            show_logs "$2" "false"
            ;;
        logs-follow)
            show_logs "50" "true"
            ;;
        clean-logs)
            clean_logs
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
